"use client";

import { format } from "date-fns";
import Link from "next/link";

import { ContractStatus, SerializedContract } from "@repo/server/src/types/contract";

import { Button } from "../ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { ClipboardDocumentListIcon } from "@heroicons/react/24/solid";

interface AthleteContractListProps {
  contracts: SerializedContract[];
  isLoading?: boolean;
  onRefresh?: () => void;
}

export function AthleteContractList({
  contracts,
  isLoading = false,
}: AthleteContractListProps) {

  const getStatusColor = (status: string) => {
    switch (status) {
      case ContractStatus.PENDING_ATHLETE_SIGNATURE:
        return "tw-bg-blue-100 tw-text-blue-800";
      case ContractStatus.ATHLETE_SIGNED:
        return "tw-bg-green-100 tw-text-green-800";
      case ContractStatus.PENDING_PAYMENT:
        return "tw-bg-orange-100 tw-text-orange-800";
      case ContractStatus.PAID:
        return "tw-bg-blue-100 tw-text-blue-800";
      case ContractStatus.AWAITING_DELIVERABLES:
        return "tw-bg-purple-100 tw-text-purple-800";
      case ContractStatus.FULFILLED:
        return "tw-bg-emerald-100 tw-text-emerald-800";
      case ContractStatus.CANCELLED:
        return "tw-bg-red-100 tw-text-red-800";
      case ContractStatus.EXPIRED:
        return "tw-bg-gray-100 tw-text-gray-600";
      default:
        return "tw-bg-gray-100 tw-text-gray-800";
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getStatusLabel = (status: string) => {
    return status.replace(/_/g, ' ');
  };

  const getActionButton = (contract: SerializedContract) => {
    const isExpired = contract.expiresAt && new Date(contract.expiresAt) < new Date();
    
    switch (contract.status) {
      case ContractStatus.PENDING_ATHLETE_SIGNATURE:
        if (isExpired) {
          return (
            <Badge variant="outline" className="tw-text-red-600 tw-border-red-200">
              Expired
            </Badge>
          );
        }
        return (
          <Link href={`/app/athlete/contracts/${contract.id}`}>
            <Button size="sm" className="tw-text-black">
              Review & Sign
            </Button>
          </Link>
        );
      case ContractStatus.ATHLETE_SIGNED:
        return (
          <Badge variant="outline" className="tw-text-green-600 tw-border-green-200">
            ✓ Athlete Signed
          </Badge>
        );
      case ContractStatus.PENDING_BRAND_SIGNATURE:
        return (
          <Badge variant="outline" className="tw-text-blue-600 tw-border-blue-200">
            Awaiting Brand Signature
          </Badge>
        );
      case ContractStatus.BRAND_SIGNED:
        return (
          <Badge variant="outline" className="tw-text-green-600 tw-border-green-200">
            ✓ Fully Signed
          </Badge>
        );
      case ContractStatus.PENDING_PAYMENT:
        return (
          <Badge variant="outline" className="tw-text-yellow-600 tw-border-yellow-200">
            Awaiting Payment
          </Badge>
        );
      case ContractStatus.PAID:
        return (
          <Badge variant="outline" className="tw-text-green-600 tw-border-green-200">
            ✓ Paid
          </Badge>
        );
      case ContractStatus.AWAITING_DELIVERABLES:
        return (
          <Link href={`/app/campaign/${contract.campaignId}?brandView=false`}>
          <Badge variant="outline" className="tw-text-purple-600 tw-border-purple-200">
            📋 Submit Deliverables
          </Badge>
          </Link>
        );
      case ContractStatus.FULFILLED:
        return (
          <Badge variant="outline" className="tw-text-emerald-600 tw-border-emerald-200">
            ✓ Completed
          </Badge>
        );
      case ContractStatus.CANCELLED:
        return (
          <Badge variant="outline" className="tw-text-red-600 tw-border-red-200">
            Cancelled
          </Badge>
        );
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <div className="tw-space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="tw-animate-pulse">
            <CardHeader>
              <div className="tw-h-4 tw-bg-gray-200 tw-rounded tw-w-3/4"></div>
              <div className="tw-h-3 tw-bg-gray-200 tw-rounded tw-w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="tw-space-y-2">
                <div className="tw-h-3 tw-bg-gray-200 tw-rounded"></div>
                <div className="tw-h-3 tw-bg-gray-200 tw-rounded tw-w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="tw-space-y-6">
      {/* Contract List */}
      <div className="tw-space-y-4">
        {contracts.length === 0 ? (
          <Card>
            <CardContent className="tw-py-12 tw-text-center !tw-pt-12">
              <p className="tw-text-gray-500 tw-text-lg">No contracts found</p>
              <p className="tw-text-gray-400 tw-text-sm">
                Contracts will appear here when brands send them to you for signature.
              </p>
            </CardContent>
          </Card>
        ) : (
          contracts.map((contract) => {
            const isExpired = contract.expiresAt && new Date(contract.expiresAt) < new Date();
            const needsAttention = contract.status === ContractStatus.PENDING_ATHLETE_SIGNATURE && !isExpired;
            
            return (
              <Card 
                key={contract.id} 
                className={`tw-hover:shadow-md tw-transition-shadow ${
                  needsAttention ? 'tw-ring-1 tw-ring-aims-primary' : ''
                }`}
              >
                <CardHeader>
                  <div className="tw-flex tw-justify-between tw-items-start">
                    <div className="tw-flex-1">
                      <CardTitle className="tw-text-lg tw-font-semibold">
                        {contract.title.split(' - ')[0]} - <Link href={`/app/campaign/${contract.campaignId}`} className="tw-underline hover:tw-text-aims-primary">{contract.title.split(' - ')[1] ? `${contract.title.split(' - ')[1]}` : ''}</Link>
                      </CardTitle>
                      <p className="tw-text-sm tw-text-gray-600 tw-mt-1">
                        Contract #{contract.contractNumber} • Version {contract.version}
                      </p>
                      {needsAttention && (
                        <p className="tw-text-sm tw-text-aims-primary tw-font-medium tw-mt-1">
                          ⚡ Action Required: Review and sign this contract
                        </p>
                      )}
                    </div>
                    <div className="tw-flex tw-items-center tw-gap-2">
                      <Badge className={getStatusColor(contract.status)}>
                        {getStatusLabel(contract.status)}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="tw-grid tw-grid-cols-1 md:tw-grid-cols-4 tw-gap-4 tw-mb-4">
                    <div>
                      <p className="tw-text-sm tw-font-medium tw-text-gray-500">Received</p>
                      <p className="tw-text-sm">{format(new Date(contract.createdAt), 'MMM dd, yyyy')}</p>
                    </div>
                    <div>
                      <p className="tw-text-sm tw-font-medium tw-text-gray-500">Campaign Period</p>
                      <p className="tw-text-sm">
                        {format(new Date(contract.terms.campaignDuration.startDate), 'MMM dd')} - {format(new Date(contract.terms.campaignDuration.endDate), 'MMM dd, yyyy')}
                      </p>
                    </div>
                    <div>
                      <p className="tw-text-sm tw-font-medium tw-text-gray-500">Total Compensation</p>
                      <p className="tw-text-sm tw-font-semibold tw-text-green-600">
                        {formatCurrency(contract.terms.totalCompensation)}
                      </p>
                    </div>
                    <div>
                      <p className="tw-text-sm tw-font-medium tw-text-gray-500">Deliverables</p>
                      <p className="tw-text-sm">{contract.terms.deliverables.length} items</p>
                    </div>
                  </div>

                  {/* Expiration Warning */}
                  {contract.expiresAt && contract.status === ContractStatus.PENDING_ATHLETE_SIGNATURE && (
                    <div className={`tw-mb-4 tw-p-3 tw-rounded-lg tw-border ${
                      isExpired 
                        ? 'tw-bg-red-50 tw-border-red-200' 
                        : 'tw-bg-yellow-50 tw-border-yellow-200'
                    }`}>
                      <p className={`tw-text-sm ${
                        isExpired ? 'tw-text-red-800' : 'tw-text-yellow-800'
                      }`}>
                        <span className="tw-font-medium">
                          {isExpired ? 'Expired:' : 'Expires:'}
                        </span> {format(new Date(contract.expiresAt), 'MMM dd, yyyy')}
                      </p>
                    </div>
                  )}

                  {/* Quick Actions */}
                  <div className="tw-flex tw-flex-wrap tw-gap-2 tw-pt-4 tw-border-t">
                    <Link href={`/app/athlete/contracts/${contract.id}`}>
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                    </Link>
                    
                    {contract.pdfUrl && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(contract.pdfUrl, '_blank')}
                      >
                        View PDF
                      </Button>
                    )}
                    
                    {getActionButton(contract)}
                  </div>
                </CardContent>
              </Card>
            );
          })
        )}
      </div>
    </div>
  );
}
